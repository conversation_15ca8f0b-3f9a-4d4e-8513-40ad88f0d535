import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Dict, Optional
import matplotlib.pyplot as plt

from base_fun import IC, PRE, gen_graph


# ---------------------------
# 算子统计类
# ---------------------------
class OperatorStatistics:
    """统计各个算子更新成功次数的类"""

    def __init__(self):
        self.reflect_success = 0      # 反射成功次数
        self.expand_success = 0       # 扩展成功次数
        self.contract_outside_success = 0  # 外部收缩成功次数
        self.contract_inside_success = 0   # 内部收缩成功次数
        self.shrink_success = 0       # 收缩回退成功次数

        # 总尝试次数
        self.reflect_attempts = 0
        self.expand_attempts = 0
        self.contract_outside_attempts = 0
        self.contract_inside_attempts = 0
        self.shrink_attempts = 0

    def record_reflect(self, success: bool):
        """记录反射操作结果"""
        self.reflect_attempts += 1
        if success:
            self.reflect_success += 1

    def record_expand(self, success: bool):
        """记录扩展操作结果"""
        self.expand_attempts += 1
        if success:
            self.expand_success += 1

    def record_contract_outside(self, success: bool):
        """记录外部收缩操作结果"""
        self.contract_outside_attempts += 1
        if success:
            self.contract_outside_success += 1

    def record_contract_inside(self, success: bool):
        """记录内部收缩操作结果"""
        self.contract_inside_attempts += 1
        if success:
            self.contract_inside_success += 1

    def record_shrink(self, success: bool):
        """记录收缩回退操作结果"""
        self.shrink_attempts += 1
        if success:
            self.shrink_success += 1

    def get_success_rates(self) -> Dict[str, float]:
        """获取各算子的成功率"""
        rates = {}
        if self.reflect_attempts > 0:
            rates['reflect'] = self.reflect_success / self.reflect_attempts
        if self.expand_attempts > 0:
            rates['expand'] = self.expand_success / self.expand_attempts
        if self.contract_outside_attempts > 0:
            rates['contract_outside'] = self.contract_outside_success / self.contract_outside_attempts
        if self.contract_inside_attempts > 0:
            rates['contract_inside'] = self.contract_inside_success / self.contract_inside_attempts
        if self.shrink_attempts > 0:
            rates['shrink'] = self.shrink_success / self.shrink_attempts
        return rates

    def get_summary(self) -> Dict[str, Dict[str, int]]:
        """获取统计摘要"""
        return {
            'reflect': {'success': self.reflect_success, 'attempts': self.reflect_attempts},
            'expand': {'success': self.expand_success, 'attempts': self.expand_attempts},
            'contract_outside': {'success': self.contract_outside_success, 'attempts': self.contract_outside_attempts},
            'contract_inside': {'success': self.contract_inside_success, 'attempts': self.contract_inside_attempts},
            'shrink': {'success': self.shrink_success, 'attempts': self.shrink_attempts}
        }

    def print_statistics(self):
        """打印统计信息"""
        print("\n=== 算子统计信息 ===")
        summary = self.get_summary()
        rates = self.get_success_rates()

        for op_name, stats in summary.items():
            success = stats['success']
            attempts = stats['attempts']
            rate = rates.get(op_name, 0.0)
            print(f"{op_name:15}: {success:4d}/{attempts:4d} ({rate:6.2%})")

        total_success = sum(stats['success'] for stats in summary.values())
        total_attempts = sum(stats['attempts'] for stats in summary.values())
        overall_rate = total_success / total_attempts if total_attempts > 0 else 0.0
        print(f"{'总计':15}: {total_success:4d}/{total_attempts:4d} ({overall_rate:6.2%})")


# 全局统计实例
operator_stats = OperatorStatistics()


def get_operator_statistics() -> OperatorStatistics:
    """获取全局算子统计实例"""
    return operator_stats


def reset_operator_statistics():
    """重置全局算子统计"""
    global operator_stats
    operator_stats = OperatorStatistics()


# ---------------------------
# 离散 NM（DM）各步骤函数
# ---------------------------
#节点的度作为节点评分
def node_score(G: nx.Graph, v: int) -> float:
    return float(G.degree(v))

#调用PRE函数计算目标值
def objective(G: nx.Graph, seed_set: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int) -> float:
    # 适应度：直接使用 PRE（值越大越好）
    return PRE(G, seed_set, p, neighbors, max_hop=max_hop)


# 计算单纯形内各顶点（种子集合）的适应度（越大越好）
def evaluate_simplex(G: nx.Graph, simplex: List[Set[int]], p: float, neighbors: Dict[int, List[int]], max_hop: int) -> List[Tuple[float, Set[int]]]:
    scored = [(objective(G, s, p, neighbors, max_hop), s) for s in simplex]
    scored.sort(key=lambda t: t[0], reverse=True)
    return scored


#根据度中心性初始化种群,单纯复形
def degree_initialization(G: nx.Graph, n: int, k: int, random_seed: Optional[int] = None) -> List[Set[int]]:

    nodes: List[int] = list(G.nodes())
    if len(nodes) < k:
        raise ValueError("图的节点数量小于所需的种子集合大小 k")

    degree_sorted_nodes: List[int] = sorted(nodes, key=lambda node: G.degree(node), reverse=True)

    solutions: List[Set[int]] = []
    for _ in range(n + 1):
        position: List[int] = degree_sorted_nodes[:k]
        for i in range(k):
            if random.random() > 0.5:
                available_nodes = [node for node in nodes if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        solutions.append(set(position))

    return solutions


# 计算除了最差解以外的“离散质心”（频率最高/度更高的节点优先）
def centroid_discrete(G: nx.Graph, simplex_sets: List[Set[int]], exclude_set: Set[int], k: int) -> Set[int]:
    # 统计除最差解外所有解中节点的出现频率
    node_frequency = Counter()
    for solution_set in simplex_sets:
        if solution_set is exclude_set:
            continue
        node_frequency.update(solution_set)

    # 按频率和节点度排序选择候选节点
    candidates = sorted(node_frequency.items(), key=lambda t: (t[1], node_score(G, t[0])), reverse=True)
    chosen_nodes = [node for node, _ in candidates[:k]]

    # 如果候选节点不足k个，补充高度节点
    if len(chosen_nodes) < k:
        all_nodes: List[int] = list(G.nodes())
        remaining_nodes = [node for node in all_nodes if node not in chosen_nodes]
        remaining_nodes.sort(key=lambda node: node_score(G, node), reverse=True)
        chosen_nodes += remaining_nodes[: (k - len(chosen_nodes))]

    # 对最终选出的 k 个节点进行随机替换：对每个节点以 0.5 概率，用网络中不在当前集合的随机节点替换
    # 确保替换后的集合元素唯一且总数保持为 k
    try:
        import random as _rnd
        chosen_set: Set[int] = set(chosen_nodes[:k])
        all_nodes_set: Set[int] = set(G.nodes())
        available_nodes: Set[int] = all_nodes_set - chosen_set
        if available_nodes:
            chosen_list = list(chosen_set)
            for node in chosen_list:
                if _rnd.random() < 0.3 and available_nodes:
                    # 从未被选中的节点中随机挑一个替换当前节点
                    new_node = _rnd.choice(tuple(available_nodes))
                    chosen_set.remove(node)
                    chosen_set.add(new_node)
                    # 更新可用池，避免重复选入
                    available_nodes.discard(new_node)
                    available_nodes.add(node)
        chosen_nodes = list(chosen_set)
    except Exception:
        # 若出现异常（极少数边界情况），退回到原始选择
        pass

    return set(chosen_nodes[:k])


def worst_nodes(G: nx.Graph, node_set: Set[int], num_nodes: int) -> List[int]:
    # 在节点集合中选出度最小的 num_nodes 个节点
    num_nodes = max(0, min(num_nodes, len(node_set)))
    return [node for node, _ in sorted(((node, node_score(G, node)) for node in node_set), key=lambda t: t[1])[:num_nodes]]


def top_nodes(G: nx.Graph, candidate_pool: Set[int], num_nodes: int, base_set: Set[int] | None = None) -> List[int]:
    # 在候选池中选出度最大的 num_nodes 个节点（避免与基础集合重复）
    base_set = base_set or set()
    available_nodes = [node for node in candidate_pool if node not in base_set]
    available_nodes.sort(key=lambda node: node_score(G, node), reverse=True)
    return available_nodes[:max(0, num_nodes)]


# 调整集合大小到 k（若超出则丢弃度小节点，不足则补充度大节点）
def repair_size(G: nx.Graph, node_set: Set[int], target_size: int) -> Set[int]:
    if len(node_set) > target_size:
        nodes_to_remove = len(node_set) - target_size
        nodes_to_drop = worst_nodes(G, node_set, nodes_to_remove)
        node_set = set(node for node in node_set if node not in nodes_to_drop)
    elif len(node_set) < target_size:
        nodes_to_add = target_size - len(node_set)
        all_nodes: Set[int] = set(G.nodes())
        additional_nodes = top_nodes(G, all_nodes - node_set, nodes_to_add)
        node_set = set(node_set) | set(additional_nodes)
    return node_set


# 反射：将“质心”相对最差解进行反射，尝试朝好的方向探索
def reflect_step(G: nx.Graph, x_centroid: Set[int], x_worst: Set[int], alpha: float, k: int,
                verbose: bool = False, stats: Optional[OperatorStatistics] = None) -> Set[int]:
    # 反射方向向量：从最差解指向质心的方向，表示优化的方向
    direction_vector = x_centroid - x_worst  # D: 反射基向量，包含质心中存在但最差解中不存在的节点

    # 将alpha转换为整数：如果alpha < 1，则按比例计算；否则直接取整
    alpha_int = max(1, int(alpha * k)) if alpha < 1 else int(alpha)
    worst_nodes_to_replace = set(worst_nodes(G, x_centroid, alpha_int))  # W: 质心中需要被替换的最差节点
    base_nodes = x_centroid - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从方向向量中选择的优质节点
    x_reflected = base_nodes | chosen_nodes  # 反射后的解
    x_reflected = repair_size(G, x_reflected, k)

    # 使用全局统计实例（如果没有提供stats参数）
    if stats is None:
        stats = operator_stats

    if verbose:
        print(f"【反射】方向向量大小={len(direction_vector)} 被替换数={len(worst_nodes_to_replace)} -> 反射解大小={len(x_reflected)}")
    return x_reflected


# 扩展：在反射成功的基础上进一步加大步长探索
def expand_step(G: nx.Graph, x_reflected: Set[int], direction_vector: Set[int], gamma: float, k: int,
               verbose: bool = False, stats: Optional[OperatorStatistics] = None) -> Set[int]:
    """
    扩展操作：当反射操作非常成功时，沿着从质心到反射点的方向进行更大步长的探索

    Args:
        G: 网络图
        x_reflected: 反射后的解
        direction_vector: 扩展方向向量，从质心指向反射点的方向 (x_reflected - x_centroid)
                         表示超越反射点的进一步优化方向，包含反射解中存在但质心中不存在的优质节点
        gamma: 扩展系数，控制扩展的步长大小
        k: 目标解的大小
        verbose: 是否输出详细信息
        stats: 算子统计实例

    Returns:
        扩展后的解集合
    """
    # 扩展方向向量：从质心指向反射点的方向，用于沿反射成功方向进行更大步长的探索
    # direction_vector = x_reflected - x_centroid  # D: 扩展基向量，包含反射解中存在但质心中不存在的节点

    # 将gamma转换为整数：如果gamma < 1，则按比例计算；否则直接取整
    gamma_int = max(1, int(gamma * k)) if gamma < 1 else int(gamma)
    worst_nodes_to_replace = set(worst_nodes(G, x_reflected, gamma_int))  # Wp: 反射解中需要被替换的最差节点

    base_nodes = x_reflected - worst_nodes_to_replace  # 保留的基础节点集合
    # 从扩展方向向量中选择优质节点：direction_vector 是从最差解到质心的方向，包含优化方向上的高质量节点
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))
    x_expanded = base_nodes | chosen_nodes  # 扩展后的解：保留基础节点 + 方向向量中的优质节点
    x_expanded = repair_size(G, x_expanded, k)

    # 使用全局统计实例（如果没有提供stats参数）
    if stats is None:
        stats = operator_stats

    if verbose:
        print(f"【扩展】方向向量大小={len(direction_vector)} 被替换数={len(worst_nodes_to_replace)} -> 扩展解大小={len(x_expanded)}")
        print(f"【扩展】方向向量内容: {sorted(list(direction_vector))[:10]}{'...' if len(direction_vector) > 10 else ''}")
    return x_expanded


# 外部收缩：当反射优于最差解但不如次差解时，向质心方向小幅移动
def contract_outside_step(G: nx.Graph, x_centroid: Set[int], x_worst: Set[int], rho: float, k: int,
                         verbose: bool = False, stats: Optional[OperatorStatistics] = None) -> Set[int]:
    # 外部收缩方向向量：从最差解指向质心的方向，用于小幅度收缩
    direction_vector = x_centroid - x_worst  # D: 外部收缩基向量，包含质心中存在但最差解中不存在的节点

    # 将rho转换为整数：如果rho < 1，则按比例计算；否则直接取整
    rho_int = max(1, int(rho * k)) if rho < 1 else int(rho)
    worst_nodes_to_replace = set(worst_nodes(G, x_centroid, rho_int))  # Wr: 质心中需要被替换的最差节点
    base_nodes = x_centroid - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从方向向量中选择的优质节点
    x_contracted_outside = base_nodes | chosen_nodes  # 外部收缩后的解
    x_contracted_outside = repair_size(G, x_contracted_outside, k)

    # 使用全局统计实例（如果没有提供stats参数）
    if stats is None:
        stats = operator_stats

    if verbose:
        print(f"【外部收缩】被替换数={len(worst_nodes_to_replace)} -> 外收缩解大小={len(x_contracted_outside)}")
    return x_contracted_outside


# 内部收缩：当反射不优于最差解时，从最差解向质心方向靠拢
def contract_inside_step(G: nx.Graph, x_worst: Set[int], x_centroid: Set[int], rho: float, k: int,
                        verbose: bool = False, stats: Optional[OperatorStatistics] = None) -> Set[int]:
    # 内部收缩方向向量：从最差解指向质心的方向，用于从最差解向质心靠拢
    direction_vector = x_centroid - x_worst  # D: 内部收缩基向量，包含质心中存在但最差解中不存在的节点

    # 将rho转换为整数：如果rho < 1，则按比例计算；否则直接取整
    rho_int = max(1, int(rho * k)) if rho < 1 else int(rho)
    worst_nodes_to_replace = set(worst_nodes(G, x_worst, rho_int))  # Wr: 最差解中需要被替换的最差节点
    base_nodes = x_worst - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从方向向量中选择的优质节点
    x_contracted_inside = base_nodes | chosen_nodes  # 内部收缩后的解
    x_contracted_inside = repair_size(G, x_contracted_inside, k)

    # 使用全局统计实例（如果没有提供stats参数）
    if stats is None:
        stats = operator_stats

    if verbose:
        print(f"【内部收缩】被替换数={len(worst_nodes_to_replace)} -> 内收缩解大小={len(x_contracted_inside)}")
    return x_contracted_inside


# 回退（收缩）：以最优解为核心，压缩其他解以重新探索
def shrink_step(G: nx.Graph, scored_simplex: List[Tuple[float, Set[int]]], k: int, sigma: float,
               verbose: bool = False, stats: Optional[OperatorStatistics] = None) -> List[Set[int]]:
    _, x_best = scored_simplex[0]  # 获取最优解作为收缩的中心点
    new_simplex = [set(x_best)]  # 新单纯形以最优解开始

    # 收缩比例：确定从每个解中保留多少节点向最优解靠拢
    nodes_to_keep = max(1, int(round(sigma * k)))  # 每个解向最优解收缩时保留的节点数

    for _, x_current in scored_simplex[1:]:
        # 收缩方向：从当前解向最优解收缩，保留当前解中的优质节点
        top_nodes_from_current = top_nodes(G, x_current, nodes_to_keep)  # 从当前解中选择度最高的节点
        x_shrunk = set(x_best) | set(top_nodes_from_current)  # 收缩后的解：最优解 + 当前解的优质节点
        x_shrunk = repair_size(G, x_shrunk, k)  # 调整到指定大小
        new_simplex.append(x_shrunk)

    # 使用全局统计实例（如果没有提供stats参数）
    if stats is None:
        stats = operator_stats

    # 记录收缩回退操作（总是成功的，因为它是一个重置操作）
    stats.record_shrink(True)

    if verbose:
        print(f"【回退-收缩】保留比例≈{sigma} 实际保留节点数={nodes_to_keep} 新单纯形大小={len(new_simplex)}")
    return new_simplex

