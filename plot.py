# ==============================================================================
# 绘图模块 - 用于绘制算法进化过程的可视化结果
# ==============================================================================

import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib import rcParams
from typing import List, Dict, Any

# 设置matplotlib中文字体
rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 指定微软雅黑
rcParams['axes.unicode_minus'] = False  # 解决负号显示为方块的问题


def plot_evolution_results(best_history: List[float], detailed_results: Dict[str, Any], 
                          network_name: str, k: int, result_dir: str) -> str:
    """
    绘制算法进化结果图表
    
    Args:
        best_history: 每代最优适应度历史
        detailed_results: 详细结果数据
        network_name: 网络名称
        k: 种子集合大小
        result_dir: 结果保存目录
    
    Returns:
        保存的图片文件路径
    """
    # 创建图形
    plt.figure(figsize=(12, 6))
    
    # 子图1：最优个体适应度变化
    plt.subplot(1, 2, 1)
    global_best_history = detailed_results['global_best_history']
    
    # 绘制当前代最优和全局最优
    plt.plot(range(1, len(best_history) + 1), best_history, 'b-', alpha=0.6, 
             linewidth=1, label='当前代最优')
    plt.plot(range(1, len(global_best_history) + 1), global_best_history, 'r-', 
             linewidth=2, marker='o', markersize=3, label='全局最优')
    
    plt.title(f'{network_name} - 最优个体适应度进化 (k={k})')
    plt.xlabel('迭代轮次')
    plt.ylabel('适应度（PRE估计）')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.legend()
    
    # 添加最优值标注
    max_fitness = max(global_best_history)
    max_gen = global_best_history.index(max_fitness) + 1
    plt.annotate(f'最优: {max_fitness:.4f}\n第{max_gen}代', 
                xy=(max_gen, max_fitness), 
                xytext=(max_gen + len(global_best_history) * 0.1, max_fitness),
                arrowprops=dict(arrowstyle='->', color='red', alpha=0.7),
                fontsize=10, ha='left')
    
    # 子图2：种群适应度标准差变化
    plt.subplot(1, 2, 2)
    fitness_stats = [gen['fitness_stats'] for gen in detailed_results['generations']]
    stds = [stat['std'] for stat in fitness_stats]
    means = [stat['mean'] for stat in fitness_stats]
    
    # 绘制标准差变化
    plt.plot(range(1, len(stds) + 1), stds, 'g-', linewidth=2, marker='s', 
             markersize=3, label='适应度标准差')
    
    plt.title(f'{network_name} - 种群适应度标准差变化 (k={k})')
    plt.xlabel('迭代轮次')
    plt.ylabel('适应度标准差')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.legend()
    
    # 添加收敛性分析
    if len(stds) > 10:
        final_std = np.mean(stds[-10:])  # 最后10代的平均标准差
        plt.axhline(y=final_std, color='orange', linestyle='--', alpha=0.7, 
                   label=f'后10代均值: {final_std:.4f}')
        plt.legend()
    
    plt.tight_layout()
    
    # 保存图片
    plot_file = os.path.join(result_dir, f"evolution_curves_k{k}.png")
    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存
    
    return plot_file


def plot_fitness_distribution(detailed_results: Dict[str, Any], network_name: str, 
                             k: int, result_dir: str) -> str:
    """
    绘制适应度分布图（可选的额外分析图）
    
    Args:
        detailed_results: 详细结果数据
        network_name: 网络名称
        k: 种子集合大小
        result_dir: 结果保存目录
    
    Returns:
        保存的图片文件路径
    """
    plt.figure(figsize=(10, 6))
    
    # 提取每代的适应度统计
    fitness_stats = [gen['fitness_stats'] for gen in detailed_results['generations']]
    generations = range(1, len(fitness_stats) + 1)
    
    means = [stat['mean'] for stat in fitness_stats]
    stds = [stat['std'] for stat in fitness_stats]
    mins = [stat['min'] for stat in fitness_stats]
    maxs = [stat['max'] for stat in fitness_stats]
    
    # 绘制适应度范围
    plt.fill_between(generations, mins, maxs, alpha=0.2, color='blue', label='适应度范围')
    plt.fill_between(generations, 
                     [m - s for m, s in zip(means, stds)], 
                     [m + s for m, s in zip(means, stds)], 
                     alpha=0.4, color='green', label='均值±标准差')
    
    plt.plot(generations, means, 'g-', linewidth=2, label='平均适应度')
    plt.plot(generations, maxs, 'r-', linewidth=1, label='最大适应度')
    plt.plot(generations, mins, 'b-', linewidth=1, label='最小适应度')
    
    plt.title(f'{network_name} - 种群适应度分布演化 (k={k})')
    plt.xlabel('迭代轮次')
    plt.ylabel('适应度（PRE估计）')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.legend()
    
    plt.tight_layout()
    
    # 保存图片
    plot_file = os.path.join(result_dir, f"fitness_distribution_k{k}.png")
    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    return plot_file


def create_summary_plot(detailed_results: Dict[str, Any], network_name: str, 
                       k: int, result_dir: str) -> str:
    """
    创建算法性能总结图
    
    Args:
        detailed_results: 详细结果数据
        network_name: 网络名称
        k: 种子集合大小
        result_dir: 结果保存目录
    
    Returns:
        保存的图片文件路径
    """
    plt.figure(figsize=(8, 6))
    
    # 提取关键信息
    global_best_history = detailed_results['global_best_history']
    final_fitness = detailed_results['final_best_fitness']
    best_generation = detailed_results['global_best_generation']
    total_runtime = detailed_results.get('total_runtime', 0)
    ic_influence = detailed_results.get('ic_evaluation', {}).get('estimated_influence', 0)
    
    # 绘制全局最优历史
    plt.plot(range(1, len(global_best_history) + 1), global_best_history, 'r-', 
             linewidth=3, marker='o', markersize=4)
    
    # 标注关键点
    plt.scatter([best_generation + 1], [final_fitness], color='red', s=100, 
               zorder=5, label=f'最优解 (第{best_generation + 1}代)')
    
    plt.title(f'{network_name} 算法性能总结 (k={k})')
    plt.xlabel('迭代轮次')
    plt.ylabel('全局最优适应度（PRE估计）')
    plt.grid(True, linestyle='--', alpha=0.5)
    
    # 添加性能信息文本框
    info_text = f'最优适应度: {final_fitness:.4f}\n'
    info_text += f'发现代数: {best_generation + 1}\n'
    info_text += f'IC估计影响力: {ic_influence:.2f}\n'
    info_text += f'运行时间: {total_runtime:.2f}秒'
    
    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)
    
    plt.legend()
    plt.tight_layout()
    
    # 保存图片
    plot_file = os.path.join(result_dir, f"summary_k{k}.png")
    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    return plot_file
