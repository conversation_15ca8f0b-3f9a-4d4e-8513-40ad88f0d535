============================================================
离散Nelder-Mead算法详细结果报告
============================================================

【基本信息】
网络名称: blog
种子集合大小: 50
单纯形维度: 30
传播概率: 0.05
最大迭代次数: 100
算法参数: α=1.5, γ=1, ρ=1.5, σ=0.25
PRE递推轮数: 5

【最终结果】
全局最优适应度: 165.457790
最优解发现代数: 25
IC模型估计影响力: 164.64
蒙特卡洛模拟次数: 1000
总运行时间: 65.83秒
最优种子集合: [3, 6, 10, 20, 25, 33, 41, 64, 73, 76, 98, 106, 108, 110, 115, 119, 126, 129, 141, 143, 152, 169, 178, 193, 197, 201, 221, 232, 238, 249, 264, 336, 368, 388, 391, 401, 411, 430, 439, 448, 492, 538, 610, 623, 661, 698, 826, 888, 1171, 1565]

【进化历史概览】
初始适应度: 134.196305
最终适应度: 165.457790
适应度提升: 31.261485
总改进次数: 16
主要改进节点:
  第2代: 137.027272
  第3代: 143.995776
  第8代: 146.400185
  第9代: 148.247985
  第10代: 150.636927
  第11代: 153.413629
  第12代: 155.029336
  第13代: 156.632410
  第15代: 158.226276
  第16代: 159.257519

【每代进化详情】
代数	当前最优	全局最优	平均值	标准差	是否更新
------------------------------------------------------------
0	134.1963	134.1963	119.2124	8.0456	否
1	129.5160	134.1963	119.1986	6.3127	否
2	137.0273	137.0273	118.8158	7.4521	否
3	143.9958	143.9958	120.6686	7.0752	否
4	134.3052	143.9958	120.2618	5.9816	否
5	136.7361	143.9958	122.4962	6.0957	否
6	140.2387	143.9958	123.9797	6.0898	否
7	141.8578	143.9958	125.7779	5.6589	否
8	146.4002	146.4002	128.2035	5.3245	否
9	148.2480	148.2480	128.1613	6.6869	否
10	150.6369	150.6369	129.4956	7.4075	否
11	153.4136	153.4136	129.6465	8.1144	否
12	155.0293	155.0293	130.2117	7.5316	否
13	156.6324	156.6324	130.5755	7.1019	否
14	149.1781	156.6324	131.2769	5.5053	否
15	158.2263	158.2263	133.2969	7.5915	否
16	159.2575	159.2575	133.6265	7.2272	否
17	160.6168	160.6168	134.7187	7.2886	否
18	161.1625	161.1625	134.8097	7.7071	否
19	162.6064	162.6064	135.5726	7.2013	否
20	146.4791	162.6064	134.9671	6.4837	否
21	163.7860	163.7860	138.5355	7.4567	否
22	164.8766	164.8766	139.6955	7.8337	否
23	164.8766	164.8766	141.3936	7.3267	否
24	164.8766	164.8766	143.2116	7.1047	否
25	165.4578	165.4578	144.9349	6.9514	否
26	165.4578	165.4578	146.4098	6.8374	否
27	165.4578	165.4578	147.6783	6.5721	否
28	165.4578	165.4578	149.4442	6.1492	否
29	165.4578	165.4578	151.2829	5.7531	否
30	165.4578	165.4578	152.9471	5.4690	否
31	165.4578	165.4578	154.3066	5.2273	否
32	165.4578	165.4578	155.7552	4.8876	否
33	165.4578	165.4578	157.1265	4.5721	否
34	165.4578	165.4578	158.3160	4.2776	否
35	165.4578	165.4578	159.5524	3.9764	否
36	165.4578	165.4578	160.5935	3.5489	否
37	165.4578	165.4578	161.4793	3.0578	否
38	165.4578	165.4578	162.3133	2.6257	否
39	165.4578	165.4578	163.0753	2.0843	否
40	165.4578	165.4578	163.6430	1.5999	否
41	165.4578	165.4578	164.0952	1.1367	否
42	165.4578	165.4578	164.4089	0.8681	否
43	165.4578	165.4578	164.6676	0.6598	否
44	165.4578	165.4578	164.8119	0.4071	否
45	165.4578	165.4578	164.8498	0.3785	否
46	165.4578	165.4578	164.9280	0.1989	否
47	165.4578	165.4578	164.9856	0.2087	否
48	165.4578	165.4578	165.0808	0.1958	否
49	165.4578	165.4578	165.0913	0.1924	否
50	165.4578	165.4578	165.0915	0.1922	否
51	165.4578	165.4578	165.0967	0.1885	否
52	165.4578	165.4578	165.4578	0.0000	否
53	165.4578	165.4578	165.4578	0.0000	否
54	165.4578	165.4578	165.4578	0.0000	否
55	165.4578	165.4578	165.4578	0.0000	否
56	165.4578	165.4578	165.4578	0.0000	否
57	165.4578	165.4578	165.4578	0.0000	否
58	165.4578	165.4578	165.4578	0.0000	否
59	165.4578	165.4578	165.4578	0.0000	否
60	165.4578	165.4578	165.4578	0.0000	否
61	165.4578	165.4578	165.4578	0.0000	否
62	165.4578	165.4578	165.4578	0.0000	否
63	165.4578	165.4578	165.4578	0.0000	否
64	165.4578	165.4578	165.4578	0.0000	否
65	165.4578	165.4578	165.4578	0.0000	否
66	165.4578	165.4578	165.4578	0.0000	否
67	165.4578	165.4578	165.4578	0.0000	否
68	165.4578	165.4578	165.4578	0.0000	否
69	165.4578	165.4578	165.4578	0.0000	否
70	165.4578	165.4578	165.4578	0.0000	否
71	165.4578	165.4578	165.4578	0.0000	否
72	165.4578	165.4578	165.4578	0.0000	否
73	165.4578	165.4578	165.4578	0.0000	否
74	165.4578	165.4578	165.4578	0.0000	否
75	165.4578	165.4578	165.4578	0.0000	否
76	165.4578	165.4578	165.4578	0.0000	否
77	165.4578	165.4578	165.4578	0.0000	否
78	165.4578	165.4578	165.4578	0.0000	否
79	165.4578	165.4578	165.4578	0.0000	否
80	165.4578	165.4578	165.4578	0.0000	否
81	165.4578	165.4578	165.4578	0.0000	否
82	165.4578	165.4578	165.4578	0.0000	否
83	165.4578	165.4578	165.4578	0.0000	否
84	165.4578	165.4578	165.4578	0.0000	否
85	165.4578	165.4578	165.4578	0.0000	否
86	165.4578	165.4578	165.4578	0.0000	否
87	165.4578	165.4578	165.4578	0.0000	否
88	165.4578	165.4578	165.4578	0.0000	否
89	165.4578	165.4578	165.4578	0.0000	否
90	165.4578	165.4578	165.4578	0.0000	否
91	165.4578	165.4578	165.4578	0.0000	否
92	165.4578	165.4578	165.4578	0.0000	否
93	165.4578	165.4578	165.4578	0.0000	否
94	165.4578	165.4578	165.4578	0.0000	否
95	165.4578	165.4578	165.4578	0.0000	否
96	165.4578	165.4578	165.4578	0.0000	否
97	165.4578	165.4578	165.4578	0.0000	否
98	165.4578	165.4578	165.4578	0.0000	否
99	165.4578	165.4578	165.4578	0.0000	否

============================================================
报告生成完成
